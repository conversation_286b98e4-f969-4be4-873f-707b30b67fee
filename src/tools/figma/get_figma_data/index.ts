import { z } from "zod";
import yaml from "js-yaml";
import { FigmaService } from "../lib/figma-service.js";
import type { FigmaAuthOptions } from "../lib/types.js";
import { extractDownloadableNodes as extractNodes } from "../lib/node-extractor.js";

export const getFigmaDataTool = {
  name: "get_figma_data",
  config: {
    title: "Get Figma Data",
    description: "When the nodeId cannot be obtained, obtain the layout information about the entire Figma file",
    inputSchema: {
      fileKey: z
        .string()
        .describe(
          "The key of the Figma file to fetch, often found in a provided URL like figma.com/(file|design)/<fileKey>/..."
        ),
      nodeId: z
        .string()
        .optional()
        .describe(
          "The ID of the node to fetch, often found as URL parameter node-id=<nodeId>, always use if provided"
        ),
      depth: z
        .number()
        .optional()
        .describe(
          "OPTIONAL. Do NOT use unless explicitly requested by the user. Controls how many levels deep to traverse the node tree"
        ),
      extractDownloadableNodes: z
        .boolean()
        .optional()
        .describe(
          "OPTIONAL. When true, extracts and returns only downloadable nodes (images and icons) in a format suitable for download_figma_images tool"
        )
    }
  },
  handler: async (args: any) => {
    const { fileKey, nodeId, depth, extractDownloadableNodes } = args;

    try {
      // 确保 .env 文件被加载
      const { config } = await import("dotenv");
      const { resolve } = await import("path");
      config({ path: resolve(process.cwd(), ".env") });

      // 从环境变量获取认证信息
      const authOptions: FigmaAuthOptions = {
        figmaApiKey: process.env.FIGMA_API_KEY || "",
        figmaOAuthToken: process.env.FIGMA_OAUTH_TOKEN || "",
        useOAuth: !!process.env.FIGMA_OAUTH_TOKEN
      };

      // 调试信息
      console.log("Debug - Current working directory:", process.cwd());
      console.log("Debug - FIGMA_API_KEY exists:", !!process.env.FIGMA_API_KEY);
      console.log("Debug - FIGMA_OAUTH_TOKEN exists:", !!process.env.FIGMA_OAUTH_TOKEN);

      // 检查是否有认证信息
      if (!authOptions.figmaApiKey && !authOptions.figmaOAuthToken) {
        return {
          isError: true,
          content: [{
            type: "text" as const,
            text: "Error: No Figma authentication found. Please set FIGMA_API_KEY or FIGMA_OAUTH_TOKEN environment variable."
          }]
        };
      }

      const figmaService = new FigmaService(authOptions);

      console.log(
        `Fetching ${
          depth ? `${depth} layers deep` : "all layers"
        } of ${nodeId ? `node ${nodeId} from file` : `full file`} ${fileKey}`
      );

      let file;
      if (nodeId) {
        file = await figmaService.getNode(fileKey, nodeId, depth);
      } else {
        file = await figmaService.getFile(fileKey, depth);
      }

      console.log(`Successfully fetched file: ${file.name}`);
      const { nodes, globalVars, ...metadata } = file;

      console.log("GlobalVars keys:", Object.keys(globalVars.styles));
      console.log("Number of nodes:", nodes.length);

      let result: any;
      let formattedResult: string;

      if (extractDownloadableNodes) {
        // 提取可下载的节点数组
        const downloadableNodes = extractNodes(file);
        console.log("Extracted downloadable nodes:", downloadableNodes.length);
        console.log("Image nodes:", downloadableNodes.filter((n: any) => n.imageRef).length);
        console.log("Vector nodes:", downloadableNodes.filter((n: any) => !n.imageRef).length);

        // 直接返回节点数组，格式化为 download_figma_images 期望的格式
        result = downloadableNodes.map((node: any) => ({
          nodeId: node.nodeId,
          fileName: node.fileName,
          ...(node.imageRef && { imageRef: node.imageRef })
        }));

        // 对于可下载节点，返回 JSON 格式以便直接使用
        console.log("Generating JSON result for downloadable nodes");
        formattedResult = JSON.stringify(result, null, 2);
      } else {
        // 返回原始的层级结构
        result = {
          metadata,
          nodes,
          globalVars
        };

        // 对于层级结构，返回 YAML 格式
        console.log("Generating YAML result from file");
        formattedResult = yaml.dump(result);
      }

      console.log("Sending result to client");
      return {
        content: [{ type: "text" as const, text: formattedResult }]
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : JSON.stringify(error);
      console.error(`Error fetching file ${fileKey}:`, message);
      return {
        isError: true,
        content: [{ type: "text" as const, text: `Error fetching file: ${message}` }]
      };
    }
  }
};
