/**
 * 节点提取工具，用于将层级节点转换为可下载的图片数组
 */

import type { SimplifiedNode, SimplifiedDesign, GlobalVars } from "./types.js";

export interface DownloadableNode {
  nodeId: string;
  fileName: string;
  imageRef?: string;
  nodeType: string;
  nodeName: string;
}

/**
 * 构建父子节点关系映射
 */
function buildParentMap(node: SimplifiedNode, parentMap: Map<string, SimplifiedNode>, parent?: SimplifiedNode) {
  if (parent) {
    parentMap.set(node.id, parent);
  }
  node.children?.forEach(child => buildParentMap(child, parentMap, node));
}

/**
 * 遍历节点并收集可下载的节点
 */
function traverseAndCollectNodes(
  node: SimplifiedNode,
  parentMap: Map<string, SimplifiedNode>,
  globalVars: GlobalVars,
  downloadableNodes: DownloadableNode[]
) {
  // 1. 如果是图片，直接添加到下载数组中
  if (hasImageFill(node, globalVars)) {
    addDownloadableNode(node, globalVars, downloadableNodes);
  }
  // 2. 如果是VECTOR，判断父节点逻辑
  else if (node.type === "VECTOR" || node.type === "IMAGE-SVG") {
    const parent = parentMap.get(node.id);
    if (parent && shouldDownloadParentForVector(parent, globalVars)) {
      addDownloadableNode(parent, globalVars, downloadableNodes);
    } else {
      addDownloadableNode(node, globalVars, downloadableNodes);
    }
  }

  // 递归遍历子节点
  node.children?.forEach(child => traverseAndCollectNodes(child, parentMap, globalVars, downloadableNodes));
}

/**
 * 添加可下载节点到数组中
 */
function addDownloadableNode(nodeToDownload: SimplifiedNode, globalVars: GlobalVars, downloadableNodes: DownloadableNode[]) {
  const downloadableNode = createDownloadableNode(nodeToDownload, globalVars);
  if (downloadableNode) {
    downloadableNodes.push(downloadableNode);
  }
}

/**
 * 从简化设计中提取可下载的节点
 */
export function extractDownloadableNodes(design: SimplifiedDesign): DownloadableNode[] {
  const downloadableNodes: DownloadableNode[] = [];
  const parentMap = new Map<string, SimplifiedNode>();

  // 构建父子节点关系映射
  design.nodes.forEach(node => buildParentMap(node, parentMap));

  // 遍历并收集可下载的节点
  design.nodes.forEach(node => traverseAndCollectNodes(node, parentMap, design.globalVars, downloadableNodes));

  // 基于node的id去重
  const uniqueNodes = new Map<string, DownloadableNode>();
  downloadableNodes.forEach(node => uniqueNodes.set(node.nodeId, node));

  return Array.from(uniqueNodes.values());
}

/**
 * 检查节点是否有图片填充
 */
function hasImageFill(node: SimplifiedNode, globalVars: GlobalVars): boolean {
  if (node.fills) {
    const fills = globalVars.styles[node.fills];
    if (Array.isArray(fills)) {
      return fills.some((fill: any) => fill.type === "IMAGE" && fill.imageRef);
    }
  }
  return false;
}

/**
 * 检查节点是否为文本节点
 */
function isTextNode(node: SimplifiedNode): boolean {
  return node.type === "TEXT" || (node.text !== undefined && node.text !== "");
}

/**
 * 检查是否应该为VECTOR子节点下载其父节点
 * 判断父节点的子节点中，没有图片节点，没有文本节点
 */
function shouldDownloadParentForVector(parent: SimplifiedNode, globalVars: GlobalVars): boolean {
  if (!parent.children || parent.children.length === 0) {
    return false;
  }
  return !parent.children.some(child => hasImageFill(child, globalVars) || isTextNode(child));
}

/**
 * 创建可下载节点对象
 */
function createDownloadableNode(node: SimplifiedNode, globalVars: GlobalVars): DownloadableNode | null {
  const baseNode: DownloadableNode = {
    nodeId: node.id,
    fileName: sanitizeFileName(node.name),
    nodeType: node.type,
    nodeName: node.name
  };

  // 首先检查图片填充（最高优先级）
  if (node.fills) {
    const fills = globalVars.styles[node.fills];
    if (Array.isArray(fills)) {
      const imageFill = fills.find((fill: any) => fill.type === "IMAGE" && fill.imageRef);
      if (imageFill) {
        baseNode.imageRef = imageFill.imageRef;
        baseNode.fileName = ensureExtension(baseNode.fileName, "png");
        return baseNode;
      }
    }
  }

  // Vector节点默认为SVG
  if (node.type === "VECTOR" || node.type === "IMAGE-SVG") {
    baseNode.fileName = ensureExtension(baseNode.fileName, "svg");
    return baseNode;
  }

  // 其他节点默认为PNG
  baseNode.fileName = ensureExtension(baseNode.fileName, "png");
  return baseNode;
}

/**
 * 清理文件名以确保文件系统安全使用
 */
function sanitizeFileName(name: string): string {
  return name
    .replace(/[^a-zA-Z0-9\s\-_]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 将空格替换为连字符
    .toLowerCase()
    .substring(0, 50); // 限制长度
}

/**
 * 确保文件名具有正确的扩展名
 */
function ensureExtension(fileName: string, extension: string): string {
  const hasExtension = fileName.toLowerCase().endsWith(`.${extension}`);
  return hasExtension ? fileName : `${fileName}.${extension}`;
}
